// Analytics Manager for tracking user interactions and system metrics
export interface AnalyticsEvent {
  name: string
  properties?: Record<string, any>
  timestamp?: Date
  userId?: string
  sessionId?: string
}

export interface AnalyticsConfig {
  enabled: boolean
  debug: boolean
  trackPageViews: boolean
  trackClicks: boolean
  trackErrors: boolean
}

export class AnalyticsManager {
  private config: AnalyticsConfig
  private events: AnalyticsEvent[] = []
  private sessionId: string

  constructor(config: Partial<AnalyticsConfig> = {}) {
    this.config = {
      enabled: process.env.NODE_ENV === 'production',
      debug: process.env.NODE_ENV === 'development',
      trackPageViews: true,
      trackClicks: true,
      trackErrors: true,
      ...config
    }
    
    this.sessionId = this.generateSessionId()
    
    if (this.config.enabled) {
      this.initializeTracking()
    }
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private initializeTracking() {
    if (typeof window === 'undefined') return

    // Track page views
    if (this.config.trackPageViews) {
      this.trackPageView()
    }

    // Track clicks
    if (this.config.trackClicks) {
      document.addEventListener('click', this.handleClick.bind(this))
    }

    // Track errors
    if (this.config.trackErrors) {
      window.addEventListener('error', this.handleError.bind(this))
      window.addEventListener('unhandledrejection', this.handleUnhandledRejection.bind(this))
    }
  }

  private handleClick(event: MouseEvent) {
    const target = event.target as HTMLElement
    if (target) {
      this.track('click', {
        element: target.tagName.toLowerCase(),
        text: target.textContent?.slice(0, 100),
        className: target.className,
        id: target.id,
        href: (target as HTMLAnchorElement).href
      })
    }
  }

  private handleError(event: ErrorEvent) {
    this.track('error', {
      message: event.message,
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
      stack: event.error?.stack
    })
  }

  private handleUnhandledRejection(event: PromiseRejectionEvent) {
    this.track('unhandled_rejection', {
      reason: event.reason?.toString(),
      stack: event.reason?.stack
    })
  }

  public track(eventName: string, properties?: Record<string, any>) {
    if (!this.config.enabled) return

    const event: AnalyticsEvent = {
      name: eventName,
      properties,
      timestamp: new Date(),
      sessionId: this.sessionId
    }

    this.events.push(event)

    if (this.config.debug) {
      console.log('Analytics Event:', event)
    }

    // In a real implementation, you would send this to your analytics service
    this.sendToAnalyticsService(event)
  }

  public trackPageView(path?: string) {
    if (typeof window === 'undefined') return

    this.track('page_view', {
      path: path || window.location.pathname,
      referrer: document.referrer,
      title: document.title,
      url: window.location.href
    })
  }

  public trackUser(userId: string, properties?: Record<string, any>) {
    this.track('user_identified', {
      userId,
      ...properties
    })
  }

  public trackConversion(conversionType: string, value?: number, properties?: Record<string, any>) {
    this.track('conversion', {
      type: conversionType,
      value,
      ...properties
    })
  }

  private async sendToAnalyticsService(event: AnalyticsEvent) {
    try {
      // In a real implementation, send to your analytics service
      // await fetch('/api/analytics/track', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(event)
      // })
      
      // For now, just store locally
      if (typeof window !== 'undefined') {
        const stored = localStorage.getItem('analytics_events') || '[]'
        const events = JSON.parse(stored)
        events.push(event)
        localStorage.setItem('analytics_events', JSON.stringify(events.slice(-100))) // Keep last 100 events
      }
    } catch (error) {
      if (this.config.debug) {
        console.error('Failed to send analytics event:', error)
      }
    }
  }

  public getEvents(): AnalyticsEvent[] {
    return [...this.events]
  }

  public clearEvents() {
    this.events = []
    if (typeof window !== 'undefined') {
      localStorage.removeItem('analytics_events')
    }
  }

  public setConfig(config: Partial<AnalyticsConfig>) {
    this.config = { ...this.config, ...config }
  }
}

// Global analytics instance
export const analytics = new AnalyticsManager()

// React hook for analytics
export function useAnalytics() {
  return {
    track: analytics.track.bind(analytics),
    trackPageView: analytics.trackPageView.bind(analytics),
    trackUser: analytics.trackUser.bind(analytics),
    trackConversion: analytics.trackConversion.bind(analytics)
  }
}
