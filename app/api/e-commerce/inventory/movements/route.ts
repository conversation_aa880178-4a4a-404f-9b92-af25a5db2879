import { NextResponse } from 'next/server'
import { inventoryService } from '@/lib/ecommerce/services/inventory-service'

export async function GET(request: Request) {
  try {
    const url = new URL(request.url)
    const productId = url.searchParams.get('productId') ?? undefined
    const variantId = url.searchParams.get('variantId') ?? undefined

    const movements = await inventoryService.getStockMovements(productId, variantId)

    return NextResponse.json({
      success: true,
      data: movements
    })
  } catch (error) {
    console.error('Error fetching stock movements:', error)
    return NextResponse.json(
      { success: false, error: (error as Error).message },
      { status: 500 }
    )
  }
}