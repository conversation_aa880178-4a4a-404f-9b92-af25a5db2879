"use client"

import React, { useState } from 'react'
import {
  ArrowLeft,
  Store,
  Plus,
  Layers,
  Sparkles,
  Settings,
  Package,
  Wand2,
  FileText,
  Layout
} from 'lucide-react'

import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
} from '@/components/ui/sidebar'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { EditorType } from './unified-editor-layout'

// Import page builder components directly (now using store)
import { BlockLibrary } from '@/lib/page-builder/components/block-library'
import { ComponentTree } from '@/lib/page-builder/components/component-tree'
import {
  AiChatPanel,
  NextJSLayoutGenerator,
  ComponentLibrary,
  AIVisualEditorLayout
} from '@/lib/ai-visual-editor'

interface EditorSidebarProps extends React.ComponentProps<typeof Sidebar> {
  editorType: EditorType
  onBackToAdmin?: () => void
}

export function EditorSidebar({ 
  editorType, 
  onBackToAdmin, 
  ...props 
}: EditorSidebarProps) {
  const [activeTab, setActiveTab] = useState<'blocks' | 'ai' | 'structure' | 'settings'>('blocks')

  // Get editor-specific configuration
  const getEditorConfig = () => {
    switch (editorType) {
      case 'page':
        return {
          title: 'Page Builder',
          subtitle: 'Visual Page Editor',
          icon: FileText,
          color: 'bg-pink-600',
          tabs: [
            { id: 'blocks', label: 'Blocks', icon: Plus },
            { id: 'ai', label: 'AI Assistant', icon: Sparkles },
            { id: 'structure', label: 'Structure', icon: Layers },
            { id: 'settings', label: 'Settings', icon: Settings }
          ]
        }
      case 'layout':
        return {
          title: 'Layout Builder',
          subtitle: 'Visual Layout Editor',
          icon: Layout,
          color: 'bg-purple-600',
          tabs: [
            { id: 'ai', label: 'AI Designer', icon: Wand2 },
            { id: 'blocks', label: 'Sections', icon: Package },
            { id: 'structure', label: 'Structure', icon: Layers },
            { id: 'settings', label: 'Settings', icon: Settings }
          ]
        }
      case 'unified':
        return {
          title: 'CMS Builder',
          subtitle: 'Unified Content Editor',
          icon: Store,
          color: 'bg-blue-600',
          tabs: [
            { id: 'ai', label: 'AI Generator', icon: Sparkles },
            { id: 'blocks', label: 'Blocks', icon: Plus },
            { id: 'structure', label: 'Structure', icon: Layers },
            { id: 'settings', label: 'Settings', icon: Settings }
          ]
        }
      default:
        return {
          title: 'Editor',
          subtitle: 'Visual Editor',
          icon: Store,
          color: 'bg-gray-600',
          tabs: []
        }
    }
  }

  const config = getEditorConfig()
  const IconComponent = config.icon

  // Page builder component wrapper (now using store)
  const PageBuilderComponent = ({ component }: { component: 'blocks' | 'structure' }) => {
    if (component === 'blocks') {
      return <BlockLibrary />
    } else if (component === 'structure') {
      return <ComponentTree />
    }
    return null
  }

  // Safe layout builder component wrapper
  const SafeLayoutBuilderComponent = ({ component }: { component: 'ai' | 'blocks' }) => {
    // Only render if we're actually in a layout builder context
    if (editorType !== 'layout') {
      return (
        <div className="p-4">
          <div className="text-center py-8 text-gray-400">
            <Layout className="h-8 w-8 mx-auto mb-2" />
            <p className="text-sm">Layout Builder {component}</p>
            <p className="text-xs text-muted-foreground mt-1">
              Available when editing layouts
            </p>
          </div>
        </div>
      )
    }

    if (component === 'ai') {
      return (
        <div className="h-full">
          <NextJSLayoutGenerator />
        </div>
      )
    }

    if (component === 'blocks') {
      return (
        <div className="h-full">
          <ComponentLibrary />
        </div>
      )
    }

    return null
  }

  // Render tab content based on editor type and active tab
  const renderTabContent = () => {
    if (editorType === 'page') {
      switch (activeTab) {
        case 'blocks':
          return (
            <div className="h-full">
              <PageBuilderComponent component="blocks" />
            </div>
          )
        case 'ai':
          return (
            <div className="h-full">
              <AiChatPanel />
            </div>
          )
        case 'structure':
          return (
            <div className="h-full">
              <PageBuilderComponent component="structure" />
            </div>
          )
        case 'settings':
          return (
            <div className="p-4">
              <div className="text-sm text-muted-foreground">
                Page settings panel will be implemented here
              </div>
            </div>
          )
        default:
          return null
      }
    }

    if (editorType === 'layout') {
      switch (activeTab) {
        case 'ai':
          return <SafeLayoutBuilderComponent component="ai" />
        case 'blocks':
          return <SafeLayoutBuilderComponent component="blocks" />
        case 'structure':
          return (
            <div className="p-4">
              <div className="text-sm text-muted-foreground">
                Layout structure tree will be displayed here
              </div>
            </div>
          )
        case 'settings':
          return (
            <div className="p-4">
              <div className="text-sm text-muted-foreground">
                Layout settings panel will be implemented here
              </div>
            </div>
          )
        default:
          return null
      }
    }

    if (editorType === 'unified') {
      switch (activeTab) {
        case 'ai':
          return (
            <div className="h-full">
              <AIVisualEditorLayout />
            </div>
          )
        case 'blocks':
          return <PageBuilderComponent component="blocks" />
        case 'structure':
          return <PageBuilderComponent component="structure" />
        case 'settings':
          return (
            <div className="p-4">
              <div className="text-sm text-muted-foreground">
                CMS builder settings panel
              </div>
            </div>
          )
        default:
          return null
      }
    }

    return null
  }

  return (
    <TooltipProvider>
      <Sidebar collapsible="icon" {...props}>
        <SidebarHeader>
          <SidebarMenu>
            <SidebarMenuItem>
              <SidebarMenuButton size="lg" asChild>
                <div className="flex items-center">
                  <div className={`flex aspect-square size-8 items-center justify-center rounded-lg ${config.color} text-white`}>
                    <IconComponent className="size-4" />
                  </div>
                  <div className="grid flex-1 text-left text-sm leading-tight">
                    <span className="truncate font-semibold">{config.title}</span>
                    <span className="truncate text-xs">{config.subtitle}</span>
                  </div>
                </div>
              </SidebarMenuButton>
            </SidebarMenuItem>
            {onBackToAdmin && (
              <SidebarMenuItem>
                <SidebarMenuButton size="sm" onClick={onBackToAdmin}>
                  <ArrowLeft className="h-4 w-4" />
                  <span className="truncate">Back to Admin</span>
                </SidebarMenuButton>
              </SidebarMenuItem>
            )}
          </SidebarMenu>
        </SidebarHeader>

        <SidebarContent className="p-0">
          {/* Tab Selector */}
          <SidebarGroup>
            <SidebarGroupContent>
              <SidebarMenu>
                <SidebarMenuItem>
                  <div className="grid grid-cols-2 gap-1 p-2">
                    {config.tabs.map((tab) => {
                      const TabIcon = tab.icon
                      return (
                        <Tooltip key={tab.id}>
                          <TooltipTrigger asChild>
                            <Button
                              variant={activeTab === tab.id ? 'default' : 'ghost'}
                              size="sm"
                              className="h-8 px-2"
                              onClick={() => setActiveTab(tab.id as any)}
                            >
                              <TabIcon className="h-3 w-3" />
                              <span className="ml-1 text-xs truncate">{tab.label}</span>
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent side="right">
                            {tab.label}
                          </TooltipContent>
                        </Tooltip>
                      )
                    })}
                  </div>
                </SidebarMenuItem>
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>

          <Separator />

          {/* Content Area */}
          <SidebarGroup className="flex-1">
            <SidebarGroupLabel>
              {config.tabs.find(tab => tab.id === activeTab)?.label || 'Tools'}
            </SidebarGroupLabel>
            <SidebarGroupContent className="flex-1 overflow-hidden">
              <div className="h-full">
                {renderTabContent()}
              </div>
            </SidebarGroupContent>
          </SidebarGroup>
        </SidebarContent>

        <SidebarRail />
      </Sidebar>
    </TooltipProvider>
  )
}
